#!/bin/bash

# MoFlash Setup Script
# This script sets up the MoFlash development environment

set -e

echo "🚀 Setting up MoFlash development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18.16+ and try again."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="18.16.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        print_error "Node.js version $NODE_VERSION is too old. Please install Node.js 18.16+ and try again."
        exit 1
    fi
    
    print_success "Node.js $NODE_VERSION is installed"
}

# Check if yarn is installed
check_yarn() {
    if ! command -v yarn &> /dev/null; then
        print_error "yarn is not installed. Please install yarn and try again."
        exit 1
    fi

    YARN_VERSION=$(yarn -v)
    print_success "yarn $YARN_VERSION is installed"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed. You can still run the app locally, but Docker is recommended for development."
        return 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_warning "Docker Compose is not installed. You can still run the app locally, but Docker is recommended for development."
        return 1
    fi
    
    print_success "Docker and Docker Compose are installed"
    return 0
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."

    # Install all dependencies using yarn workspaces
    yarn install

    print_success "All dependencies installed"
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "Created backend/.env from example"
    else
        print_warning "backend/.env already exists, skipping"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        print_success "Created frontend/.env from example"
    else
        print_warning "frontend/.env already exists, skipping"
    fi
}

# Setup database with Docker
setup_database_docker() {
    print_status "Setting up database with Docker..."
    
    # Start PostgreSQL and Redis
    docker-compose up -d postgres redis
    
    # Wait for PostgreSQL to be ready
    print_status "Waiting for PostgreSQL to be ready..."
    sleep 10
    
    # Run migrations and seed data
    print_status "Running database migrations..."
    cd backend && yarn db:migrate && cd ..

    print_status "Seeding database with demo data..."
    cd backend && yarn db:seed && cd ..
    
    print_success "Database setup complete"
}

# Setup database locally
setup_database_local() {
    print_status "Setting up database locally..."
    print_warning "Make sure PostgreSQL is running on localhost:5432"
    print_warning "Database: moflash_dev, User: moflash_user, Password: moflash_password"
    
    read -p "Press Enter to continue when PostgreSQL is ready..."
    
    # Run migrations and seed data
    print_status "Running database migrations..."
    cd backend && yarn db:migrate && cd ..

    print_status "Seeding database with demo data..."
    cd backend && yarn db:seed && cd ..
    
    print_success "Database setup complete"
}

# Main setup function
main() {
    echo "🎯 MoFlash Development Environment Setup"
    echo "========================================"
    
    # Check prerequisites
    check_node
    check_yarn
    
    # Check Docker availability
    DOCKER_AVAILABLE=false
    if check_docker; then
        DOCKER_AVAILABLE=true
    fi
    
    # Install dependencies
    install_dependencies
    
    # Setup environment files
    setup_env_files
    
    # Setup database
    if [ "$DOCKER_AVAILABLE" = true ]; then
        echo ""
        echo "Choose database setup method:"
        echo "1) Docker (recommended)"
        echo "2) Local PostgreSQL"
        read -p "Enter your choice (1 or 2): " choice
        
        case $choice in
            1)
                setup_database_docker
                ;;
            2)
                setup_database_local
                ;;
            *)
                print_error "Invalid choice. Please run the script again."
                exit 1
                ;;
        esac
    else
        setup_database_local
    fi
    
    echo ""
    echo "🎉 Setup complete!"
    echo "=================="
    echo ""
    echo "Next steps:"
    echo "1. Start the development servers:"
    if [ "$DOCKER_AVAILABLE" = true ]; then
        echo "   Docker: docker-compose up -d"
        echo "   Local:  yarn dev"
    else
        echo "   yarn dev"
    fi
    echo ""
    echo "2. Access the application:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:3001"
    echo "   API Docs: http://localhost:3001/api/v1/docs"
    echo ""
    echo "3. Demo accounts:"
    echo "   Email: <EMAIL> | Password: demo123456"
    echo "   Email: <EMAIL> | Password: demo123456"
    echo "   Email: <EMAIL> | Password: demo123456"
    echo ""
    print_success "Happy coding! 🚀"
}

# Run main function
main "$@"

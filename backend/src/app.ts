import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
// import compression from "compression";
// import rateLimit from "express-rate-limit";
import dotenv from "dotenv";

// Import utilities
import { logger, morganStream } from "@/utils/logger";
import { testDatabaseConnection } from "@/utils/database";

// Import middleware
import { errorHandler, notFoundHandler } from "@/middleware/errorHandler";

// Import routes
import authRoutes from "@/routes/authRoutes";
import deckRoutes from "@/routes/deckRoutes";
import cardRoutes from "@/routes/cardRoutes";
import studyRoutes from "@/routes/studyRoutes";

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Trust proxy for rate limiting and IP detection
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  })
);

// CORS configuration
const corsOptions = {
  origin: process.env["CORS_ORIGIN"] || "http://localhost:3000",
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-API-Key"],
};

app.use(cors(corsOptions));

// Compression middleware
// app.use(compression()); // Temporarily disabled due to TypeScript issues

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Logging middleware
app.use(morgan("combined", { stream: morganStream }));

// Rate limiting - temporarily disabled due to TypeScript issues
// const limiter = rateLimit({
//   windowMs: parseInt(process.env["RATE_LIMIT_WINDOW_MS"] || "900000"), // 15 minutes
//   max: parseInt(process.env["RATE_LIMIT_MAX_REQUESTS"] || "100"), // limit each IP to 100 requests per windowMs
//   message: {
//     success: false,
//     error: "Too many requests from this IP, please try again later",
//   },
//   standardHeaders: true,
//   legacyHeaders: false,
// });

// app.use("/api", limiter);

// Health check endpoint
app.get("/health", async (_req, res) => {
  try {
    // Check database connection
    const dbHealthy = await testDatabaseConnection();

    res.json({
      success: true,
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: process.env["npm_package_version"] || "1.0.0",
      environment: process.env["NODE_ENV"] || "development",
      database: dbHealthy ? "connected" : "disconnected",
    });
  } catch (error) {
    logger.error("Health check failed", error);
    res.status(503).json({
      success: false,
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: "Service unavailable",
    });
  }
});

// API routes
const API_VERSION = process.env["API_VERSION"] || "v1";
app.use(`/api/${API_VERSION}/auth`, authRoutes);
app.use(`/api/${API_VERSION}/decks`, deckRoutes);
app.use(`/api/${API_VERSION}/cards`, cardRoutes);
app.use(`/api/${API_VERSION}/study`, studyRoutes);

// API documentation endpoint
app.get(`/api/${API_VERSION}/docs`, (_req, res) => {
  res.json({
    success: true,
    message: "MoFlash API Documentation",
    version: API_VERSION,
    endpoints: {
      auth: {
        "POST /auth/register": "Register a new user",
        "POST /auth/login": "Login user",
        "POST /auth/refresh": "Refresh access token",
        "POST /auth/logout": "Logout user",
        "GET /auth/profile": "Get user profile",
        "PUT /auth/profile": "Update user profile",
      },
      decks: {
        "GET /decks": "Get user decks",
        "POST /decks": "Create new deck",
        "GET /decks/public": "Get public decks",
        "GET /decks/:id": "Get deck by ID",
        "PUT /decks/:id": "Update deck",
        "DELETE /decks/:id": "Delete deck",
      },
      cards: {
        "GET /cards/deck/:deckId": "Get cards in deck",
        "POST /cards/deck/:deckId": "Create new card",
        "GET /cards/:id": "Get card by ID",
        "PUT /cards/:id": "Update card",
        "DELETE /cards/:id": "Delete card",
      },
      study: {
        "GET /study/due": "Get due cards",
        "GET /study/new": "Get new cards",
        "POST /study/sessions": "Create study session",
        "PUT /study/sessions/:id/end": "End study session",
        "POST /study/reviews": "Submit card review",
        "GET /study/stats": "Get study statistics",
      },
    },
    documentation: "https://docs.moflash.com",
  });
});

// Root endpoint
app.get("/", (_req, res) => {
  res.json({
    success: true,
    message: "Welcome to MoFlash API",
    version: API_VERSION,
    documentation: `/api/${API_VERSION}/docs`,
    health: "/health",
  });
});

// 404 handler for undefined routes
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM received, shutting down gracefully");
  process.exit(0);
});

process.on("SIGINT", () => {
  logger.info("SIGINT received, shutting down gracefully");
  process.exit(0);
});

// Unhandled promise rejection handler
process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Uncaught exception handler
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

// Start server
const PORT = process.env["PORT"] || 3001;

const startServer = async () => {
  try {
    // Test database connection
    const dbConnected = await testDatabaseConnection();
    if (dbConnected) {
      logger.info("Database connection established");
    } else {
      logger.warn("Database connection failed, but starting server anyway");
    }

    // Start server
    app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env["NODE_ENV"] || "development"}`);
      logger.info(
        `API Documentation: http://localhost:${PORT}/api/${API_VERSION}/docs`
      );
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Only start server if this file is run directly
if (require.main === module) {
  startServer();
}

export default app;

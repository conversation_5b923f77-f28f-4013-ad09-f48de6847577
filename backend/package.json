{"name": "moflash-backend", "version": "1.0.0", "description": "MoFlash Backend API", "main": "dist/app.js", "scripts": {"dev": "nodemon -r tsconfig-paths/register src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:setup": "yarn db:migrate && yarn db:seed", "db:migrate": "ts-node -r tsconfig-paths/register database/migrate.ts", "db:seed": "ts-node -r tsconfig-paths/register database/seed.ts", "db:reset": "ts-node database/reset.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.1", "redis": "^4.6.7", "ioredis": "^5.3.2", "zod": "^3.21.4", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "nodemailer": "^6.9.3", "node-cron": "^3.0.2", "winston": "^3.9.0", "dotenv": "^16.3.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/passport": "^1.0.12", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/pg": "^8.10.2", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "@types/node-cron": "^3.0.7", "@types/uuid": "^9.0.2", "@types/node": "^20.4.2", "@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "typescript": "^5.1.6", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.6.1", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "tsconfig-paths": "^4.2.0"}, "engines": {"node": ">=18.16.0"}}
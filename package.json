{"name": "moflash", "version": "1.0.0", "description": "Next-generation flashcard application with AI-powered learning", "main": "index.js", "scripts": {"dev": "concurrently \"yarn dev:backend\" \"yarn dev:frontend\"", "dev:backend": "cd backend && yarn dev", "dev:frontend": "cd frontend && yarn dev", "build": "yarn build:backend && yarn build:frontend", "build:backend": "cd backend && yarn build", "build:frontend": "cd frontend && yarn build", "test": "yarn test:backend && yarn test:frontend", "test:backend": "cd backend && yarn test", "test:frontend": "cd frontend && yarn test", "install:all": "yarn install && cd backend && yarn install && cd ../frontend && yarn install", "db:setup": "cd backend && yarn db:setup", "db:migrate": "cd backend && yarn db:migrate", "db:seed": "cd backend && yarn db:seed", "start": "yarn start:backend", "start:backend": "cd backend && yarn start", "lint": "yarn lint:backend && yarn lint:frontend", "lint:backend": "cd backend && yarn lint", "lint:frontend": "cd frontend && yarn lint"}, "keywords": ["flashcards", "learning", "education", "spaced-repetition", "ai", "study"], "author": "MoFlash Team", "license": "PROPRIETARY", "private": true, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.16.0", "yarn": ">=1.22.0"}, "workspaces": ["backend", "frontend"]}